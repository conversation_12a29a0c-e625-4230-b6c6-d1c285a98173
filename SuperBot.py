import redis
import uuid
from pyrogram import Client, filters, idle
from pytonapi import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pyromod import listen
from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery, Message


# Config #
API_KEY = "AEK6RODNSU5FXQAAAAADMVJJE2BQTF3KD7ZZZWOCHF7EOFXPTFD44X7E7IAA3HFWPD3D6IY"
BOT_TOKEN = "7816479210:AAFVma5VY4rglHGQR_zofoHR3RJ-gLy_vh4"
APP_ID = 3656868
API_HASH = "3911d017831e5325cf9f02edbb3bcae1"
OWNER_ID = 5957520831
SudoList = [1925760763, 929431022, 1701103153,2077888891, 1419968900, 7851780978, 1418191661, 7716619699]
GroupId = -1002118684055
# Clients #
bot = Client("zwn_bot", api_id=APP_ID, api_hash=API_HASH, bot_token=BOT_TOKEN)
redis_client = redis.Redis(decode_responses=True, db=7)


# Functions #
def generate_random_id():
    return str(uuid.uuid4())


def store_request_data(request_id, user_id, data_type, data):
    redis_client.hset(f"request:{request_id}", mapping={
        "user_id": user_id,
        "data_type": data_type,
        "data": data
    })


def get_request_data(request_id):
    return redis_client.hgetall(f"request:{request_id}")


async def NFT(user):
    try:
        tonapi = AsyncTonapi(api_key=API_KEY)
        await tonapi.dns.get_info(f"{user.replace('@', '')}.t.me")
        return True
    except Exception as e:
        return False


def is_admin_or_owner(user_id):
    if user_id == OWNER_ID:
        return True
    elif user_id == 1925760763:
        return True
    elif user_id in SudoList:
        return True
    else:
        return redis_client.sismember('admins', user_id)


async def send_request(user, data, data_type, admin):
    request_id = generate_random_id()
    store_request_data(request_id, user.id, data_type, data)

    await bot.send_message(
        admin,
        f"A user requested a {data_type}:\n"
        f"ID: {user.id}\n"
        f"First Name: {user.first_name}\n"
        f"Last Name: {user.last_name}\n"
        f"Username: @{user.username}\n"
        f"Requested {data_type.capitalize()}: {data}\n",
        reply_markup=InlineKeyboardMarkup(
            [
                [InlineKeyboardButton("Accept - Default Channel", callback_data=f"accept_default/{request_id}")],
                [InlineKeyboardButton("Accept - All Channels", callback_data=f"accept_all/{request_id}")],
                [InlineKeyboardButton("Reject", callback_data=f"reject/{request_id}")],
            ]
        )
    )


async def send_request_multi_users(user, usernames, admin):
    request_id = generate_random_id()
    usernames_text = "\n".join(usernames)
    store_request_data(request_id, user.id, "usernames", usernames_text)

    users_nft = ""
    for username in usernames:
        user_type = await NFT(username)
        nft = "NFT" if user_type else "NON NFT"
        users_nft += f"@{username} ( {nft} )\n"

    await bot.send_message(
        admin,
        f"A user requested multiple usernames:\n"
        f"ID: {user.id}\n"
        f"First Name: {user.first_name}\n"
        f"Last Name: {user.last_name}\n"
        f"Username: @{user.username}\n"
        f"Requested Usernames:\n{users_nft}",
        reply_markup=InlineKeyboardMarkup(
            [
                [InlineKeyboardButton("Accept - Default Channel", callback_data=f"accept_default/{request_id}")],
                [InlineKeyboardButton("Accept - All Channels", callback_data=f"accept_all/{request_id}")],
                [InlineKeyboardButton("Reject", callback_data=f"reject/{request_id}")],
            ]
        )
    )

async def send_request_NFT(user, nft_link, admin):
    request_id = generate_random_id()
    store_request_data(request_id, user.id, "NFT link", nft_link)

    await bot.send_message(
        admin,
        f"A user requested an NFT link:\n"
        f"ID: {user.id}\n"
        f"First Name: {user.first_name}\n"
        f"Last Name: {user.last_name}\n"
        f"Username: @{user.username}\n"
        f"Requested NFT Link: {nft_link}\n",
        reply_markup=InlineKeyboardMarkup(
            [
                [InlineKeyboardButton("Accept - Default Channel", callback_data=f"accept_default/{request_id}")],
                [InlineKeyboardButton("Accept - All Channels", callback_data=f"accept_all/{request_id}")],
                [InlineKeyboardButton("Reject", callback_data=f"reject/{request_id}")],
            ]
        )
    )


async def send_request_multi_NFTs(user, nft_links, admin):
    request_id = generate_random_id()
    store_request_data(request_id, user.id, "NFT links", nft_links)

    await bot.send_message(
        admin,
        f"A user requested multiple NFT links:\n"
        f"ID: {user.id}\n"
        f"First Name: {user.first_name}\n"
        f"Last Name: {user.last_name}\n"
        f"Username: @{user.username}\n"
        f"Requested NFT Links:\n{nft_links}",
        reply_markup=InlineKeyboardMarkup(
            [
                [InlineKeyboardButton("Accept - Default Channel", callback_data=f"accept_default/{request_id}")],
                [InlineKeyboardButton("Accept - All Channels", callback_data=f"accept_all/{request_id}")],
                [InlineKeyboardButton("Reject", callback_data=f"reject/{request_id}")],
            ]
        )
    )


@bot.on_callback_query()
async def handle_callback(client, callback_query: CallbackQuery):
    data = callback_query.data

    if "help" in data:
        help_text = (
            "**Bot Commands:**\n\n"
            "/start - Start interacting with the bot and record your information.\n"
            "/send_user - Send user for admin approve.\n"
            "/add_channel - Add a new channel to the bot.\n"
            "/set_default_channel - Set a channel as the default channel for posts.\n"
            "/add_admin <user_id> - Add a new admin to the bot.\n"
            "/set_message - Set the default message format with **user** as a placeholder.\n"
            "/lock_bot - Lock the bot to prevent new users from joining.\n"
            "/unlock_bot - Unlock the bot to allow new users to join.\n"
            "/broadcast - Broadcast a message to all users (owner only).\n"
            "/send_NFT - Send an NFT link for admin approval.\n"
            "/send_NFTs - Send multiple NFT links for admin approval.\n"
        )
        await callback_query.message.edit(
            help_text,
            reply_markup=InlineKeyboardMarkup(
                [
                    [InlineKeyboardButton("رجوع ↩️", callback_data="back_home")]
                ]
            )
        )

    elif "How_to_use" in data:
        text = (
            "**كيفيه استخدام البوت :**"
            "\n"
            "~ /send_user :"
            "\n"
            "لكي ترسل طلب اعلان في المزاد وتنتظر قبول او رفض الادمنيه علي المعرف."
            "\n"
            "~ /send_users :"
            "\n"
            "لارسال اكثر من معرف في اعلان المزاد."
            "\n"
            "~ /send_NFT :"
            "\n"
            "لارسال رابط NFT واحد للمزاد."
            "\n"
            "~ /send_NFTs :"
            "\n"
            "لارسال اكثر من رابط NFT في اعلان المزاد."
        )
        await callback_query.message.edit(
            text,
            reply_markup=InlineKeyboardMarkup(
                [
                    [InlineKeyboardButton("رجوع ↩️", callback_data="back_home")]
                ]
            )
        )

    elif "back_home" in data:
        if is_admin_or_owner(callback_query.from_user.id):
            welcome_message = "**مرحباً بك!** اليك اوامر البوت واستخدامه."
            command_bt = InlineKeyboardButton("اوامر البوت 📝", callback_data="help")
        else:
            welcome_message = (
                "**مرحباً بك!** في بوت مزاد ZWN."
                "\n\n"
                "~ لطريقه الاستخدام اضغط علي الازرار في الاسفل."
                "\n"
                "~ للتحدث مع مالك البوت او المطور اضغط علي زر المطور او المالك في الاسفل."

            )
            command_bt = InlineKeyboardButton("القناه 📢", url="t.me/o_u_m")

        await callback_query.message.edit(
            welcome_message,
            reply_markup=InlineKeyboardMarkup(
            [
                [
                InlineKeyboardButton("كيفيه الاستخدام ⁉️", callback_data="How_to_use"),
                command_bt
                ],
                [
                InlineKeyboardButton("المطور 👨‍💻", user_id=929431022),
                InlineKeyboardButton("مالك البوت 👑", user_id=OWNER_ID)
                ]
            ]
            )
        )

    elif "accept_default/" in data:
        identifier = data.split("/")[1]
        default_channel = redis_client.get('default_channel')
        request_data = get_request_data(identifier)

        if not request_data:
            await callback_query.message.edit("**Invalid request identifier.**")
            return

        user_id = request_data.get("user_id")
        data_type = request_data.get("data_type")
        data = request_data.get("data")

        if redis_client.get(f"request:{identifier}:state") == "accepted":
            await callback_query.message.edit("**Request already accepted.**")
            return

        if default_channel:
            if data_type == "NFT links":
                message_text = (
                    f"{data}\n\n"
                    "يمنع الكلام داخل المناقشة - ممنوع دفع سعر وعدم الشراء اذا خالفت القوانين يتم حظرك من القناة"
                    "\n\n"
                    "Auction channel - @o_u_m"
                )
            elif data_type == "NFT link":
                message_text = (
                    f"{data}\n\n"
                    "يمنع الكلام داخل المناقشة - ممنوع دفع سعر وعدم الشراء اذا خالفت القوانين يتم حظرك من القناة"
                    "\n\n"
                    "Auction channel - @o_u_m"
                )
            elif data_type == "usernames":
                users_nft = ""
                for username in data.split("\n"):
                    user_type = await NFT(username)
                    nft = "NFT" if user_type else "NON NFT"
                    users_nft += f"@{username} ({nft}) | "

                message_text = (
                    f"Usernames Soom • {users_nft}\n\n"
                    "يمنع الكلام داخل المناقشة - ممنوع دفع سعر وعدم الشراء اذا خالفت القوانين يتم حظرك من القناة"
                    "\n\n"
                    "Auction channel - @o_u_m"
                )
            else:
                user_type = await NFT(data)
                nft = "NFT" if user_type else "NON NFT"
                message_text = (
                    f"Username {'NFT ' if user_type else ''}Soom • @{data}\n\n"
                    "يمنع الكلام داخل المناقشة - ممنوع دفع سعر وعدم الشراء اذا خالفت القوانين يتم حظرك من القناة"
                    "\n\n"
                    "Auction channel - @o_u_m"
                )
            msg = await bot.send_message(default_channel, message_text)
            redis_client.setex(f"request:{identifier}:state", 86400, "accepted")
            await bot.send_message(
                user_id,
                f"**Your request has been accepted and posted in the default channel.**\n**~ Message:** {msg.link}"
            )
            await callback_query.message.edit("**Request accepted and posted.**")
        else:
            await callback_query.answer("**No default channel is set.**", show_alert=True)

    elif "accept_all/" in data:
        identifier = data.split("/")[1]
        channels = redis_client.smembers('channels')
        request_data = get_request_data(identifier)

        if not request_data:
            await callback_query.message.edit("**Invalid request identifier.**")
            return

        user_id = request_data.get("user_id")
        data_type = request_data.get("data_type")
        data = request_data.get("data")

        if redis_client.get(f"request:{identifier}:state") == "accepted":
            await callback_query.message.edit("**Request already accepted.**")
            return

        list_ = ""
        if data_type == "NFT links":
            message_text = (
                    f"{data}\n\n"
                    "يمنع الكلام داخل المناقشة - ممنوع دفع سعر وعدم الشراء اذا خالفت القوانين يتم حظرك من القناة"
                    "\n\n"
                    "Auction channel - @o_u_m"
                )
        elif data_type == "NFT link":
            message_text = (
                    f"{data}\n\n"
                    "يمنع الكلام داخل المناقشة - ممنوع دفع سعر وعدم الشراء اذا خالفت القوانين يتم حظرك من القناة"
                    "\n\n"
                    "Auction channel - @o_u_m"
                )
        elif data_type == "usernames":
            users_nft = ""
            for username in data.split("\n"):
                user_type = await NFT(username)
                nft = "NFT" if user_type else "NON NFT"
                users_nft += f"@{username} ({nft}) | "

            message_text = (
                f"Usernames Soom • {users_nft}\n\n"
                "يمنع الكلام داخل المناقشة - ممنوع دفع سعر وعدم الشراء اذا خالفت القوانين يتم حظرك من القناة"
                "\n\n"
                "Auction channel - @o_u_m"
            )
        else:
            user_type = await NFT(data)
            nft = "NFT" if user_type else "NON NFT"
            message_text = (
                f"Username {'NFT ' if user_type else ''}Soom • @{data}\n\n"
                "يمنع الكلام داخل المناقشة - ممنوع دفع سعر وعدم الشراء اذا خالفت القوانين يتم حظرك من القناة"
                "\n\n"
                "Auction channel - @o_u_m"
            )

        for channel in channels:
            msg = await bot.send_message(channel, message_text)

            list_ += msg.link + " | "

            redis_client.setex(f"request:{identifier}:state", 86400, "accepted")
            await bot.send_message(
            user_id,
            f"**Your request has been accepted and posted in all channels.**\n**~ Channels:**\n{list_}"
            )
            await callback_query.message.edit("**Request accepted and posted in all channels.**")

    elif "reject/" in data:
        identifier = data.split("/")[1]
        request_data = get_request_data(identifier)

        if not request_data:
            await callback_query.message.edit("**Invalid request identifier.**")
            return

        user_id = request_data.get("user_id")

        if redis_client.get(f"request:{identifier}:state") == "rejected":
            await callback_query.message.edit("**Request already rejected.**")
        else:
            redis_client.setex(f"request:{identifier}:state", 86400, "rejected")
            await bot.send_message(
                user_id,
                "**Your request has been rejected.**"
            )
            await callback_query.message.edit("**Request rejected.**")


@bot.on_message(filters.private & filters.command("start"))
async def start(client, message: Message):
    user_id = message.from_user.id
    first_name = message.from_user.first_name
    last_name = message.from_user.last_name
    username = message.from_user.username

    if is_admin_or_owner(user_id):
        welcome_message = "**مرحباً بك!** اليك اوامر البوت واستخدامه."
        command_bt = InlineKeyboardButton("اوامر البوت 📝", callback_data="help")
    elif redis_client.get('bot_status') == 'locked':
        await message.reply("**البوت لا يعمل الان.**")
        return
    else:
        welcome_message = (
            "**مرحباً بك!** في بوت مزاد ZWN."
            "\n\n"
            "~ لطريقه الاستخدام اضغط علي الازرار في الاسفل."
            "\n"
            "~ للتحدث مع مالك البوت او المطور اضغط علي زر المطور او المالك في الاسفل."

        )
        command_bt = InlineKeyboardButton("القناه 📢", url="t.me/o_u_m")
        if not redis_client.sismember('users', user_id):
            redis_client.sadd('users', user_id)
            await bot.send_message(
                OWNER_ID,
                f"**New user started using the bot:**\n\n"
                f"**ID:** {user_id}\n"
                f"**First Name:** {first_name}\n"
                f"**Last Name:** {last_name}\n"
                f"**Username:** @{username}\n"
            )

    await message.reply(
        welcome_message,
        reply_markup=InlineKeyboardMarkup(
            [
                [
                    InlineKeyboardButton("كيفيه الاستخدام ⁉️", callback_data="How_to_use"),
                    command_bt
                ],
                [
                    InlineKeyboardButton("المطور 👨‍💻", user_id=929431022),
                    InlineKeyboardButton("مالك البوت 👑", user_id=OWNER_ID)
                ]
            ]
        )
    )


@bot.on_message(filters.private & filters.command("send_user"))
async def send_user(client, message: Message):
    if redis_client.get('bot_status') == 'locked':
        await message.reply("**البوت مغلق الان.**")
        return

    chat = message.chat
    username_message = await chat.ask("**ارسل المعرف الان :**", filters.text)
    username = username_message.text.replace('@', '')
    user = message.from_user
    admins = redis_client.smembers("admins")

    await message.reply("**لقد تم ارسال طلبك وتتم مراجعته الان. برحاء الانتظار**")
    await send_request(user, username, "username", OWNER_ID)

    for admin in admins:
        await send_request(user, username, "username", admin)


@bot.on_message(filters.private & filters.command("send_NFT"))
async def send_NFT(client, message: Message):
    if redis_client.get('bot_status') == 'locked':
        await message.reply("**البوت مغلق الان.**")
        return

    chat = message.chat
    nft_message = await chat.ask("**ارسل رابط NFT الان (t.me/nft/...):**", filters.text)
    nft_link = nft_message.text.strip()
    user = message.from_user
    admins = redis_client.smembers("admins")

    await message.reply("**لقد تم ارسال طلبك وتتم مراجعته الان. برحاء الانتظار**")
    await send_request_NFT(user, nft_link, OWNER_ID)

    for admin in admins:
        await send_request_NFT(user, nft_link, admin)


@bot.on_message(filters.private & filters.command("send_users"))
async def send_users(client, message: Message):
    if redis_client.get('bot_status') == 'locked':
        await message.reply("**البوت مغلق الان.**")
        return

    chat = message.chat
    user = message.from_user
    admins = redis_client.smembers("admins")
    usernames = []

    await message.reply("**ارسل المعرفات واحدًا تلو الآخر. عند الانتهاء، اكتب /end لإنهاء العملية.**")

    while True:
        user_input = await chat.ask("**ارسل المعرف الان :**", filters=filters.text)
        if user_input.text == "/end":
            await user_input.reply("**تم إنهاء العملية.**")
            break
        else:
            username = user_input.text.replace('@', '').strip()
            usernames.append(username)
            await user_input.reply("**تم حفظ المعرف. ارسل التالي أو اكتب /end إذا انتهيت.**")

    if not usernames:
        await message.reply("**لم يتم إرسال أي معرفات.**")
        return

    await message.reply("**لقد تم ارسال طلبك وتتم مراجعته الان. برحاء الانتظار**")
    await send_request_multi_users(user, usernames, OWNER_ID)

    for admin in admins:
        await send_request_multi_users(user, usernames, admin)


@bot.on_message(filters.private & filters.command("send_NFTs"))
async def send_NFTs(client, message: Message):
    if redis_client.get('bot_status') == 'locked':
        await message.reply("**البوت مغلق الان.**")
        return

    chat = message.chat
    user = message.from_user
    admins = redis_client.smembers("admins")
    nft_links = []

    await message.reply("**ارسل روابط NFT واحدًا تلو الآخر. عند الانتهاء، اكتب /end لإنهاء العملية.**")

    while True:
        user_input = await chat.ask("**ارسل رابط NFT الان (t.me/nft/...):**", filters=filters.text)
        if user_input.text == "/end":
            await user_input.reply("**تم إنهاء العملية.**")
            break
        else:
            nft_link = user_input.text.strip()
            nft_links.append(nft_link)
            await user_input.reply("**تم حفظ الرابط. ارسل التالي أو اكتب /end إذا انتهيت.**")

    if not nft_links:
        await message.reply("**لم يتم إرسال أي روابط.**")
        return

    nft_links_text = "\n".join(nft_links)
    await message.reply("**لقد تم ارسال طلبك وتتم مراجعته الان. برحاء الانتظار**")
    await send_request_multi_NFTs(user, nft_links_text, OWNER_ID)

    for admin in admins:
        await send_request_multi_NFTs(user, nft_links_text, admin)


@bot.on_message(filters.private & filters.user(OWNER_ID) & filters.command("broadcast"))
async def broadcast_message(client, message: Message):
    user_ids = redis_client.smembers('users')

    chat = message.chat
    broadcast_message_ = await chat.ask("**Send broadcast message:**", filters.text)

    for user_id in user_ids:
        try:
            if message.media:
                await bot.copy_message(
                    chat_id=user_id,
                    from_chat_id=broadcast_message_.chat.id,
                    message_id=broadcast_message_.id
                )
            else:
                await bot.send_message(chat_id=user_id, text=broadcast_message_.text)
        except Exception as e:
            print(f"Failed to send message to {user_id}: {e}")

    await message.reply("**Message has been sent to all users.**")


@bot.on_message(filters.private & filters.command("add_channel"))
async def add_channel(client, message: Message):
    if not is_admin_or_owner(message.from_user.id):
        await message.reply("**You do not have the permissions to perform this action.**")
        return

    chat = message.chat
    channel_message = await chat.ask("**Please enter the channel username without @:**", filters.text)

    channel_username = channel_message.text.replace('@', '')

    if redis_client.sismember('channels', channel_username):
        await channel_message.reply("**This channel already exists.**")
    else:
        redis_client.sadd('channels', channel_username)
        await channel_message.reply(f"**Channel {channel_username} has been added.**")


@bot.on_message(filters.private & filters.command("set_default_channel"))
async def set_default_channel(client, message: Message):
    if not is_admin_or_owner(message.from_user.id):
        await message.reply("**You do not have the permissions to perform this action.**")
        return

    chat = message.chat
    channel_message = await chat.ask("**Please enter the channel username without @:**", filters.text)

    channel_username = channel_message.text.replace('@', '')

    if redis_client.sismember('channels', channel_username):
        redis_client.set('default_channel', channel_username)
        await channel_message.reply(f"**Channel {channel_username} has been set as default.**")
    else:
        await channel_message.reply("**This channel does not exist.**")


@bot.on_message(filters.private & filters.command("add_admin"))
async def add_admin(client, message: Message):
    if not is_admin_or_owner(message.from_user.id):
        await message.reply("**You do not have the permissions to perform this action.**")
        return

    args = message.text.split(maxsplit=1)
    if len(args) < 2:
        await message.reply("**Please provide the user ID of the new admin.**")
        return

    admin_id = int(args[1].strip())
    redis_client.sadd('admins', admin_id)
    await message.reply(f"**User with ID {admin_id} has been added as an admin.**")


@bot.on_message(filters.private & filters.command("rem_message"))
async def rem_message(client, message: Message):
    if not is_admin_or_owner(message.from_user.id):
        await message.reply("**You do not have the permissions to perform this action.**")
        return

    redis_client.delete('default_message')
    await message.reply("**Default message format has been removed.**")


@bot.on_message(filters.private & filters.command("set_message"))
async def set_message(client, message: Message):
    if not is_admin_or_owner(message.from_user.id):
        await message.reply("**You do not have the permissions to perform this action.**")
        return

    new_message = await message.chat.ask(
        (
            """```TAGS\nusername = #user\nnft type = #nft```"""
            "**Please enter the new message format:**\n"
        ),
        filters=filters.text
    )

    redis_client.set('default_message', new_message.text.strip())

    await new_message.reply("**Default message format has been updated.**")


@bot.on_message(filters.private & filters.command("lock_bot"))
async def lock_bot(client, message: Message):
    if not is_admin_or_owner(message.from_user.id):
        await message.reply("**You do not have the permissions to perform this action.**")
        return

    redis_client.set('bot_status', 'locked')
    await message.reply("**Bot is now locked and no new users can join.**")


@bot.on_message(filters.private & filters.command("unlock_bot"))
async def unlock_bot(client, message: Message):
    if not is_admin_or_owner(message.from_user.id):
        await message.reply("**You do not have the permissions to perform this action.**")
        return

    redis_client.set('bot_status', 'open')
    await message.reply("**Bot is now unlocked and new users can join.**")


@bot.on_message(filters.chat(GroupId) & filters.group)
async def comment_msg(cleint, message: Message):
    if "Auction channel - @o_u_m" in message.text:
        msg_text = (
            "**- تم منع الكلام في المناقشة بالمسح .**"
            "\n\n"
            "( المجموعة فقط للمزايدة أي كلام داخل المناقشة = كتم )"
            "\n"
            "**- زاود بالتدريج مع اضافة العملة .**"
            "\n"
            "**- مثال :**"
            "\n"
            "( 1$ / 1ton / 1as / 10ج )"
        )
        await message.reply(msg_text)

    elif "Auction channel - @o_u_m" in message.text:
        msg_text = (
            "**- تم منع الكلام في المناقشة بالمسح .**"
            "\n\n"
            "( المجموعة فقط للمزايدة أي كلام داخل المناقشة = كتم )"
            "\n"
            "**- زاود بالتدريج مع اضافة العملة .**"
            "\n"
            "**- مثال :**"
            "\n"
            "( 1$ / 1ton / 1as / 10ج )"
        )
        await message.reply(msg_text)

    elif "Auction channel - @o_u_m" in message.text:
        msg_text = (
            "**- تم منع الكلام في المناقشة بالمسح .**"
            "\n\n"
            "( المجموعة فقط للمزايدة أي كلام داخل المناقشة = كتم )"
            "\n"
            "**- زاود بالتدريج مع اضافة العملة .**"
            "\n"
            "**- مثال :**"
            "\n"
            "( 1$ / 1ton / 1as / 10ج )"
        )
        await message.reply(msg_text)


if __name__ == '__main__':
    bot.start()
    print(f"[/] @{bot.me.username} | Bot Start.")
    try:
        bot.send_message(OWNER_ID, "`I'm Working..`")
    except Exception as e:
        print(e)
    idle()
    redis_client.close()
