#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بوت مزاد ZWN المطور - Enhanced Auction Bot
نظام مزاد متقدم مع واجهة عربية كاملة وميزات محسنة
"""

import redis
import uuid
import asyncio
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Union
from pyrogram import Client, filters, idle
from pytonapi import AsyncTonapi
from pyromod import listen
from pyrogram.types import (
    InlineKeyboardMarkup, InlineKeyboardButton, 
    CallbackQuery, Message, ReplyKeyboardMarkup, KeyboardButton
)

# ===== إعدادات البوت =====
class BotConfig:
    API_KEY = "AEK6RODNSU5FXQAAAAADMVJJE2BQTF3KD7ZZZWOCHF7EOFXPTFD44X7E7IAA3HFWPD3D6IY"
    BOT_TOKEN = "7816479210:AAFVma5VY4rglHGQR_zofoHR3RJ-gLy_vh4"
    APP_ID = 3656868
    API_HASH = "3911d017831e5325cf9f02edbb3bcae1"
    OWNER_ID = 5957520831
    SUDO_LIST = [1925760763, 929431022, 1701103153, 2077888891, 1419968900, 7851780978, 1418191661, 7716619699]
    GROUP_ID = -1002118684055
    REDIS_DB = 7

# ===== إعداد التسجيل =====
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ===== العملاء والاتصالات =====
bot = Client("zwn_auction_bot", api_id=BotConfig.APP_ID, api_hash=BotConfig.API_HASH, bot_token=BotConfig.BOT_TOKEN)
redis_client = redis.Redis(decode_responses=True, db=BotConfig.REDIS_DB)

# ===== لوحات المفاتيح المخصصة =====
class CustomKeyboards:
    @staticmethod
    def main_menu_user() -> InlineKeyboardMarkup:
        """لوحة المفاتيح الرئيسية للمستخدمين العاديين"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("📝 إرسال معرف واحد", callback_data="send_single_user"),
                InlineKeyboardButton("👥 إرسال معرفات متعددة", callback_data="send_multiple_users")
            ],
            [
                InlineKeyboardButton("🎨 إرسال NFT واحد", callback_data="send_single_nft"),
                InlineKeyboardButton("🖼️ إرسال NFTs متعددة", callback_data="send_multiple_nfts")
            ],
            [
                InlineKeyboardButton("📊 حالة طلباتي", callback_data="my_requests"),
                InlineKeyboardButton("💰 أسعار العملات", callback_data="crypto_prices")
            ],
            [
                InlineKeyboardButton("❓ كيفية الاستخدام", callback_data="how_to_use"),
                InlineKeyboardButton("📢 القناة الرسمية", url="t.me/o_u_m")
            ],
            [
                InlineKeyboardButton("👨‍💻 المطور", user_id=929431022),
                InlineKeyboardButton("👑 مالك البوت", user_id=BotConfig.OWNER_ID)
            ]
        ])

    @staticmethod
    def main_menu_admin() -> InlineKeyboardMarkup:
        """لوحة المفاتيح الرئيسية للمشرفين"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("⚙️ إدارة القنوات", callback_data="manage_channels"),
                InlineKeyboardButton("👥 إدارة المشرفين", callback_data="manage_admins")
            ],
            [
                InlineKeyboardButton("📊 إحصائيات البوت", callback_data="bot_stats"),
                InlineKeyboardButton("🔒 قفل/فتح البوت", callback_data="toggle_bot_lock")
            ],
            [
                InlineKeyboardButton("📢 إرسال إعلان", callback_data="broadcast_message"),
                InlineKeyboardButton("⚡ الطلبات المعلقة", callback_data="pending_requests")
            ],
            [
                InlineKeyboardButton("🎯 إعدادات الرسائل", callback_data="message_settings"),
                InlineKeyboardButton("📈 تقارير النشاط", callback_data="activity_reports")
            ],
            [
                InlineKeyboardButton("🔙 العودة للقائمة العادية", callback_data="back_to_user_menu")
            ]
        ])

    @staticmethod
    def request_actions(request_id: str) -> InlineKeyboardMarkup:
        """أزرار إجراءات الطلبات المحسنة"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("✅ قبول - القناة الافتراضية", callback_data=f"accept_default/{request_id}"),
                InlineKeyboardButton("🌟 قبول - جميع القنوات", callback_data=f"accept_all/{request_id}")
            ],
            [
                InlineKeyboardButton("⏰ قبول مؤجل", callback_data=f"accept_delayed/{request_id}"),
                InlineKeyboardButton("💎 قبول مميز", callback_data=f"accept_premium/{request_id}")
            ],
            [
                InlineKeyboardButton("❌ رفض", callback_data=f"reject/{request_id}"),
                InlineKeyboardButton("🚫 حظر المستخدم", callback_data=f"ban_user/{request_id}")
            ],
            [
                InlineKeyboardButton("📝 طلب تعديل", callback_data=f"request_edit/{request_id}"),
                InlineKeyboardButton("ℹ️ تفاصيل إضافية", callback_data=f"more_info/{request_id}")
            ]
        ])

    @staticmethod
    def back_button() -> InlineKeyboardMarkup:
        """زر العودة"""
        return InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة", callback_data="back_home")]])

# ===== فئة إدارة البيانات =====
class DataManager:
    @staticmethod
    def generate_request_id() -> str:
        """توليد معرف طلب فريد"""
        return str(uuid.uuid4())

    @staticmethod
    def store_request_data(request_id: str, user_id: int, data_type: str, data: str, priority: str = "normal") -> None:
        """حفظ بيانات الطلب مع معلومات إضافية"""
        timestamp = datetime.now().isoformat()
        redis_client.hset(f"request:{request_id}", mapping={
            "user_id": user_id,
            "data_type": data_type,
            "data": data,
            "priority": priority,
            "timestamp": timestamp,
            "status": "pending"
        })
        redis_client.expire(f"request:{request_id}", 86400 * 7)  # انتهاء صلاحية بعد أسبوع

    @staticmethod
    def get_request_data(request_id: str) -> Dict:
        """استرجاع بيانات الطلب"""
        return redis_client.hgetall(f"request:{request_id}")

    @staticmethod
    def update_request_status(request_id: str, status: str) -> None:
        """تحديث حالة الطلب"""
        redis_client.hset(f"request:{request_id}", "status", status)
        redis_client.hset(f"request:{request_id}", "updated_at", datetime.now().isoformat())

    @staticmethod
    def get_user_requests(user_id: int) -> List[Dict]:
        """الحصول على طلبات المستخدم"""
        keys = redis_client.keys("request:*")
        user_requests = []
        for key in keys:
            request_data = redis_client.hgetall(key)
            if request_data.get("user_id") == str(user_id):
                request_data["request_id"] = key.split(":")[1]
                user_requests.append(request_data)
        return sorted(user_requests, key=lambda x: x.get("timestamp", ""), reverse=True)

# ===== فئة التحقق من NFT =====
class NFTValidator:
    @staticmethod
    async def check_nft_status(username: str) -> bool:
        """التحقق من حالة NFT للمعرف"""
        try:
            tonapi = AsyncTonapi(api_key=BotConfig.API_KEY)
            await tonapi.dns.get_info(f"{username.replace('@', '')}.t.me")
            return True
        except Exception as e:
            logger.error(f"خطأ في التحقق من NFT للمعرف {username}: {e}")
            return False

    @staticmethod
    async def validate_nft_link(nft_link: str) -> bool:
        """التحقق من صحة رابط NFT"""
        if not nft_link.startswith("t.me/nft/"):
            return False
        try:
            # يمكن إضافة المزيد من عمليات التحقق هنا
            return True
        except Exception:
            return False

# ===== فئة إدارة الصلاحيات =====
class PermissionManager:
    @staticmethod
    def is_owner(user_id: int) -> bool:
        """التحقق من كون المستخدم هو المالك"""
        return user_id == BotConfig.OWNER_ID

    @staticmethod
    def is_sudo(user_id: int) -> bool:
        """التحقق من كون المستخدم في قائمة السودو"""
        return user_id in BotConfig.SUDO_LIST

    @staticmethod
    def is_admin(user_id: int) -> bool:
        """التحقق من كون المستخدم مشرف"""
        return redis_client.sismember('admins', user_id)

    @staticmethod
    def is_admin_or_owner(user_id: int) -> bool:
        """التحقق من الصلاحيات الإدارية"""
        return (PermissionManager.is_owner(user_id) or 
                PermissionManager.is_sudo(user_id) or 
                PermissionManager.is_admin(user_id))

    @staticmethod
    def is_banned(user_id: int) -> bool:
        """التحقق من حظر المستخدم"""
        return redis_client.sismember('banned_users', user_id)

    @staticmethod
    def ban_user(user_id: int, reason: str = "مخالفة القوانين") -> None:
        """حظر مستخدم"""
        redis_client.sadd('banned_users', user_id)
        redis_client.hset(f"ban:{user_id}", mapping={
            "reason": reason,
            "timestamp": datetime.now().isoformat()
        })

    @staticmethod
    def unban_user(user_id: int) -> None:
        """إلغاء حظر مستخدم"""
        redis_client.srem('banned_users', user_id)
        redis_client.delete(f"ban:{user_id}")

# ===== فئة إدارة الرسائل =====
class MessageManager:
    @staticmethod
    def get_welcome_message(is_admin: bool = False) -> str:
        """رسالة الترحيب المخصصة"""
        if is_admin:
            return (
                "🎯 **مرحباً بك في لوحة التحكم الإدارية!**\n\n"
                "يمكنك من هنا إدارة جميع جوانب بوت المزاد:\n"
                "• إدارة الطلبات والموافقات\n"
                "• إضافة وإدارة القنوات\n"
                "• مراقبة الإحصائيات والتقارير\n"
                "• إرسال الإعلانات للمستخدمين\n\n"
                "اختر الإجراء المطلوب من الأزرار أدناه 👇"
            )
        else:
            return (
                "🎉 **أهلاً وسهلاً بك في بوت مزاد ZWN المطور!**\n\n"
                "🔥 **الميزات الجديدة:**\n"
                "• إرسال معرفات ومعرفات NFT متعددة\n"
                "• تتبع حالة طلباتك في الوقت الفعلي\n"
                "• عرض أسعار العملات المشفرة\n"
                "• واجهة محسنة وسهلة الاستخدام\n\n"
                "📝 **لبدء المزاد:**\n"
                "اختر نوع الطلب من الأزرار أدناه\n\n"
                "💡 **نصيحة:** تأكد من قراءة التعليمات قبل الإرسال"
            )

    @staticmethod
    def format_auction_message(data_type: str, data: str, nft_info: str = "") -> str:
        """تنسيق رسالة المزاد"""
        base_rules = (
            "\n\n🚫 **قوانين المزاد:**\n"
            "• يُمنع الكلام داخل المناقشة\n"
            "• ممنوع دفع سعر وعدم الشراء\n"
            "• زاود بالتدريج مع إضافة العملة\n"
            "• مثال: (1$ / 1TON / 1AS / 10ج)\n\n"
            "📢 **قناة المزاد:** @o_u_m"
        )

        if data_type == "username":
            return f"🎯 **مزاد معرف تيليجرام**\n\n@{data} {nft_info}{base_rules}"
        elif data_type == "usernames":
            return f"🎯 **مزاد معرفات تيليجرام متعددة**\n\n{data}{base_rules}"
        elif data_type == "NFT link":
            return f"🎨 **مزاد رابط NFT**\n\n{data}{base_rules}"
        elif data_type == "NFT links":
            return f"🖼️ **مزاد روابط NFT متعددة**\n\n{data}{base_rules}"
        else:
            return f"{data}{base_rules}"

# ===== فئة إدارة الطلبات =====
class RequestHandler:
    @staticmethod
    async def send_request_to_admins(user: Message.from_user, data: str, data_type: str, priority: str = "normal") -> str:
        """إرسال الطلب للمشرفين مع تحسينات"""
        request_id = DataManager.generate_request_id()
        DataManager.store_request_data(request_id, user.id, data_type, data, priority)

        # تحديد أيقونة الأولوية
        priority_icon = "🔥" if priority == "premium" else "⭐" if priority == "high" else "📝"
        
        # تنسيق معلومات المستخدم
        user_info = (
            f"{priority_icon} **طلب {data_type} جديد**\n\n"
            f"👤 **معلومات المستخدم:**\n"
            f"🆔 المعرف: `{user.id}`\n"
            f"📝 الاسم: {user.first_name or 'غير محدد'}\n"
            f"📝 اللقب: {user.last_name or 'غير محدد'}\n"
            f"🔗 المعرف: @{user.username or 'غير محدد'}\n"
            f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        )

        # إضافة تفاصيل البيانات حسب النوع
        if data_type == "username":
            nft_status = await NFTValidator.check_nft_status(data)
            nft_text = "✅ NFT" if nft_status else "❌ عادي"
            message_text = f"{user_info}🎯 **المعرف المطلوب:** @{data} ({nft_text})"
        elif data_type == "usernames":
            usernames_list = data.split('\n')
            formatted_usernames = ""
            for username in usernames_list:
                if username.strip():
                    nft_status = await NFTValidator.check_nft_status(username.strip())
                    nft_text = "✅ NFT" if nft_status else "❌ عادي"
                    formatted_usernames += f"• @{username.strip()} ({nft_text})\n"
            message_text = f"{user_info}👥 **المعرفات المطلوبة:**\n{formatted_usernames}"
        else:
            message_text = f"{user_info}📎 **البيانات المطلوبة:**\n{data}"

        # إرسال للمالك والمشرفين
        admin_ids = [BotConfig.OWNER_ID] + list(redis_client.smembers('admins'))
        
        for admin_id in admin_ids:
            try:
                await bot.send_message(
                    admin_id,
                    message_text,
                    reply_markup=CustomKeyboards.request_actions(request_id)
                )
            except Exception as e:
                logger.error(f"فشل في إرسال الطلب للمشرف {admin_id}: {e}")

        return request_id
