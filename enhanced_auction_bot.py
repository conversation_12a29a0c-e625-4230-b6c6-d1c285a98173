#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بوت مزاد ZWN المطور - Enhanced Auction Bot
نظام مزاد متقدم مع واجهة عربية كاملة وميزات محسنة
"""

import redis
import uuid
import asyncio
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Union
from pyrogram import Client, filters, idle
from pytonapi import AsyncTonapi
from pyromod import listen
from pyrogram.types import (
    InlineKeyboardMarkup, InlineKeyboardButton, 
    CallbackQuery, Message, ReplyKeyboardMarkup, KeyboardButton
)

# ===== إعدادات البوت =====
class BotConfig:
    API_KEY = "AEK6RODNSU5FXQAAAAADMVJJE2BQTF3KD7ZZZWOCHF7EOFXPTFD44X7E7IAA3HFWPD3D6IY"
    BOT_TOKEN = "7660986749:AAHJjAHYMFzz6EWOFParG6us1AB_q9CPjq4"
    APP_ID = 3656868
    API_HASH = "3911d017831e5325cf9f02edbb3bcae1"
    OWNER_ID = 5957520831
    SUDO_LIST = [1925760763, 929431022, 1701103153, 2077888891, 1419968900, 7851780978, 1418191661, 7716619699]
    GROUP_ID = -1002118684055
    REDIS_DB = 7

# ===== إعداد التسجيل =====
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ===== العملاء والاتصالات =====
bot = Client("zwn_auction_bot", api_id=BotConfig.APP_ID, api_hash=BotConfig.API_HASH, bot_token=BotConfig.BOT_TOKEN)
redis_client = redis.Redis(decode_responses=True, db=BotConfig.REDIS_DB)

# ===== لوحات المفاتيح المخصصة =====
class CustomKeyboards:
    @staticmethod
    def main_menu_user() -> InlineKeyboardMarkup:
        """لوحة المفاتيح الرئيسية للمستخدمين العاديين"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("📝 إرسال معرف واحد", callback_data="send_single_user"),
                InlineKeyboardButton("👥 إرسال معرفات متعددة", callback_data="send_multiple_users")
            ],
            [
                InlineKeyboardButton("🎨 إرسال NFT واحد", callback_data="send_single_nft"),
                InlineKeyboardButton("🖼️ إرسال NFTs متعددة", callback_data="send_multiple_nfts")
            ],
            [
                InlineKeyboardButton("📊 حالة طلباتي", callback_data="my_requests"),
                InlineKeyboardButton("💰 أسعار العملات", callback_data="crypto_prices")
            ],
            [
                InlineKeyboardButton("❓ كيفية الاستخدام", callback_data="how_to_use"),
                InlineKeyboardButton("📢 القناة الرسمية", url="t.me/o_u_m")
            ],
            [
                InlineKeyboardButton("👨‍💻 المطور", user_id=929431022),
                InlineKeyboardButton("👑 مالك البوت", user_id=BotConfig.OWNER_ID)
            ]
        ])

    @staticmethod
    def main_menu_admin() -> InlineKeyboardMarkup:
        """لوحة المفاتيح الرئيسية للمشرفين"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("⚙️ إدارة القنوات", callback_data="manage_channels"),
                InlineKeyboardButton("👥 إدارة المشرفين", callback_data="manage_admins")
            ],
            [
                InlineKeyboardButton("📊 إحصائيات البوت", callback_data="bot_stats"),
                InlineKeyboardButton("🔒 قفل/فتح البوت", callback_data="toggle_bot_lock")
            ],
            [
                InlineKeyboardButton("📢 إرسال إعلان", callback_data="broadcast_message"),
                InlineKeyboardButton("⚡ الطلبات المعلقة", callback_data="pending_requests")
            ],
            [
                InlineKeyboardButton("🎯 إعدادات الرسائل", callback_data="message_settings"),
                InlineKeyboardButton("📈 تقارير النشاط", callback_data="activity_reports")
            ],
            [
                InlineKeyboardButton("🔙 العودة للقائمة العادية", callback_data="back_to_user_menu")
            ]
        ])

    @staticmethod
    def request_actions(request_id: str) -> InlineKeyboardMarkup:
        """أزرار إجراءات الطلبات المحسنة"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("✅ قبول - القناة الافتراضية", callback_data=f"accept_default/{request_id}"),
                InlineKeyboardButton("🌟 قبول - جميع القنوات", callback_data=f"accept_all/{request_id}")
            ],
            [
                InlineKeyboardButton("⏰ قبول مؤجل", callback_data=f"accept_delayed/{request_id}"),
                InlineKeyboardButton("💎 قبول مميز", callback_data=f"accept_premium/{request_id}")
            ],
            [
                InlineKeyboardButton("❌ رفض", callback_data=f"reject/{request_id}"),
                InlineKeyboardButton("🚫 حظر المستخدم", callback_data=f"ban_user/{request_id}")
            ],
            [
                InlineKeyboardButton("📝 طلب تعديل", callback_data=f"request_edit/{request_id}"),
                InlineKeyboardButton("ℹ️ تفاصيل إضافية", callback_data=f"more_info/{request_id}")
            ]
        ])

    @staticmethod
    def back_button() -> InlineKeyboardMarkup:
        """زر العودة"""
        return InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة", callback_data="back_home")]])

# ===== فئة إدارة البيانات =====
class DataManager:
    @staticmethod
    def generate_request_id() -> str:
        """توليد معرف طلب فريد"""
        return str(uuid.uuid4())

    @staticmethod
    def store_request_data(request_id: str, user_id: int, data_type: str, data: str, priority: str = "normal") -> None:
        """حفظ بيانات الطلب مع معلومات إضافية"""
        timestamp = datetime.now().isoformat()
        redis_client.hset(f"request:{request_id}", mapping={
            "user_id": user_id,
            "data_type": data_type,
            "data": data,
            "priority": priority,
            "timestamp": timestamp,
            "status": "pending"
        })
        redis_client.expire(f"request:{request_id}", 86400 * 7)  # انتهاء صلاحية بعد أسبوع

    @staticmethod
    def get_request_data(request_id: str) -> Dict:
        """استرجاع بيانات الطلب"""
        return redis_client.hgetall(f"request:{request_id}")

    @staticmethod
    def update_request_status(request_id: str, status: str) -> None:
        """تحديث حالة الطلب"""
        redis_client.hset(f"request:{request_id}", "status", status)
        redis_client.hset(f"request:{request_id}", "updated_at", datetime.now().isoformat())

    @staticmethod
    def get_user_requests(user_id: int) -> List[Dict]:
        """الحصول على طلبات المستخدم"""
        keys = redis_client.keys("request:*")
        user_requests = []
        for key in keys:
            request_data = redis_client.hgetall(key)
            if request_data.get("user_id") == str(user_id):
                request_data["request_id"] = key.split(":")[1]
                user_requests.append(request_data)
        return sorted(user_requests, key=lambda x: x.get("timestamp", ""), reverse=True)

# ===== فئة التحقق من NFT =====
class NFTValidator:
    @staticmethod
    async def check_nft_status(username: str) -> bool:
        """التحقق من حالة NFT للمعرف"""
        try:
            tonapi = AsyncTonapi(api_key=BotConfig.API_KEY)
            await tonapi.dns.get_info(f"{username.replace('@', '')}.t.me")
            return True
        except Exception as e:
            logger.error(f"خطأ في التحقق من NFT للمعرف {username}: {e}")
            return False

    @staticmethod
    async def validate_nft_link(nft_link: str) -> bool:
        """التحقق من صحة رابط NFT"""
        if not nft_link.startswith("t.me/nft/"):
            return False
        try:
            # يمكن إضافة المزيد من عمليات التحقق هنا
            return True
        except Exception:
            return False

# ===== فئة إدارة الصلاحيات =====
class PermissionManager:
    @staticmethod
    def is_owner(user_id: int) -> bool:
        """التحقق من كون المستخدم هو المالك"""
        return user_id == BotConfig.OWNER_ID

    @staticmethod
    def is_sudo(user_id: int) -> bool:
        """التحقق من كون المستخدم في قائمة السودو"""
        return user_id in BotConfig.SUDO_LIST

    @staticmethod
    def is_admin(user_id: int) -> bool:
        """التحقق من كون المستخدم مشرف"""
        return redis_client.sismember('admins', user_id)

    @staticmethod
    def is_admin_or_owner(user_id: int) -> bool:
        """التحقق من الصلاحيات الإدارية"""
        return (PermissionManager.is_owner(user_id) or 
                PermissionManager.is_sudo(user_id) or 
                PermissionManager.is_admin(user_id))

    @staticmethod
    def is_banned(user_id: int) -> bool:
        """التحقق من حظر المستخدم"""
        return redis_client.sismember('banned_users', user_id)

    @staticmethod
    def ban_user(user_id: int, reason: str = "مخالفة القوانين") -> None:
        """حظر مستخدم"""
        redis_client.sadd('banned_users', user_id)
        redis_client.hset(f"ban:{user_id}", mapping={
            "reason": reason,
            "timestamp": datetime.now().isoformat()
        })

    @staticmethod
    def unban_user(user_id: int) -> None:
        """إلغاء حظر مستخدم"""
        redis_client.srem('banned_users', user_id)
        redis_client.delete(f"ban:{user_id}")

# ===== فئة إدارة الرسائل =====
class MessageManager:
    @staticmethod
    def get_welcome_message(is_admin: bool = False) -> str:
        """رسالة الترحيب المخصصة"""
        if is_admin:
            return (
                "🎯 **مرحباً بك في لوحة التحكم الإدارية!**\n\n"
                "يمكنك من هنا إدارة جميع جوانب بوت المزاد:\n"
                "• إدارة الطلبات والموافقات\n"
                "• إضافة وإدارة القنوات\n"
                "• مراقبة الإحصائيات والتقارير\n"
                "• إرسال الإعلانات للمستخدمين\n\n"
                "اختر الإجراء المطلوب من الأزرار أدناه 👇"
            )
        else:
            return (
                "🎉 **أهلاً وسهلاً بك في بوت مزاد ZWN المطور!**\n\n"
                "🔥 **الميزات الجديدة:**\n"
                "• إرسال معرفات ومعرفات NFT متعددة\n"
                "• تتبع حالة طلباتك في الوقت الفعلي\n"
                "• عرض أسعار العملات المشفرة\n"
                "• واجهة محسنة وسهلة الاستخدام\n\n"
                "📝 **لبدء المزاد:**\n"
                "اختر نوع الطلب من الأزرار أدناه\n\n"
                "💡 **نصيحة:** تأكد من قراءة التعليمات قبل الإرسال"
            )

    @staticmethod
    def format_auction_message(data_type: str, data: str, nft_info: str = "") -> str:
        """تنسيق رسالة المزاد"""
        base_rules = (
            "\n\n🚫 **قوانين المزاد:**\n"
            "• يُمنع الكلام داخل المناقشة\n"
            "• ممنوع دفع سعر وعدم الشراء\n"
            "• زاود بالتدريج مع إضافة العملة\n"
            "• مثال: (1$ / 1TON / 1AS / 10ج)\n\n"
            "📢 **قناة المزاد:** @o_u_m"
        )

        if data_type == "username":
            return f"🎯 **مزاد معرف تيليجرام**\n\n@{data} {nft_info}{base_rules}"
        elif data_type == "usernames":
            return f"🎯 **مزاد معرفات تيليجرام متعددة**\n\n{data}{base_rules}"
        elif data_type == "NFT link":
            return f"🎨 **مزاد رابط NFT**\n\n{data}{base_rules}"
        elif data_type == "NFT links":
            return f"🖼️ **مزاد روابط NFT متعددة**\n\n{data}{base_rules}"
        else:
            return f"{data}{base_rules}"

# ===== فئة إدارة الطلبات =====
class RequestHandler:
    @staticmethod
    async def send_request_to_admins(user: Message.from_user, data: str, data_type: str, priority: str = "normal") -> str:
        """إرسال الطلب للمشرفين مع تحسينات"""
        request_id = DataManager.generate_request_id()
        DataManager.store_request_data(request_id, user.id, data_type, data, priority)

        # تحديد أيقونة الأولوية
        priority_icon = "🔥" if priority == "premium" else "⭐" if priority == "high" else "📝"
        
        # تنسيق معلومات المستخدم
        user_info = (
            f"{priority_icon} **طلب {data_type} جديد**\n\n"
            f"👤 **معلومات المستخدم:**\n"
            f"🆔 المعرف: `{user.id}`\n"
            f"📝 الاسم: {user.first_name or 'غير محدد'}\n"
            f"📝 اللقب: {user.last_name or 'غير محدد'}\n"
            f"🔗 المعرف: @{user.username or 'غير محدد'}\n"
            f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        )

        # إضافة تفاصيل البيانات حسب النوع
        if data_type == "username":
            nft_status = await NFTValidator.check_nft_status(data)
            nft_text = "✅ NFT" if nft_status else "❌ عادي"
            message_text = f"{user_info}🎯 **المعرف المطلوب:** @{data} ({nft_text})"
        elif data_type == "usernames":
            usernames_list = data.split('\n')
            formatted_usernames = ""
            for username in usernames_list:
                if username.strip():
                    nft_status = await NFTValidator.check_nft_status(username.strip())
                    nft_text = "✅ NFT" if nft_status else "❌ عادي"
                    formatted_usernames += f"• @{username.strip()} ({nft_text})\n"
            message_text = f"{user_info}👥 **المعرفات المطلوبة:**\n{formatted_usernames}"
        else:
            message_text = f"{user_info}📎 **البيانات المطلوبة:**\n{data}"

        # إرسال للمالك والمشرفين
        admin_ids = [BotConfig.OWNER_ID] + list(redis_client.smembers('admins'))
        
        for admin_id in admin_ids:
            try:
                await bot.send_message(
                    admin_id,
                    message_text,
                    reply_markup=CustomKeyboards.request_actions(request_id)
                )
            except Exception as e:
                logger.error(f"فشل في إرسال الطلب للمشرف {admin_id}: {e}")

        return request_id

    @staticmethod
    async def process_request_acceptance(request_id: str, action_type: str, admin_id: int) -> Dict:
        """معالجة قبول الطلبات مع أنواع مختلفة"""
        request_data = DataManager.get_request_data(request_id)

        if not request_data:
            return {"success": False, "message": "معرف الطلب غير صحيح"}

        if request_data.get("status") != "pending":
            return {"success": False, "message": "تم معالجة هذا الطلب مسبقاً"}

        user_id = int(request_data.get("user_id"))
        data_type = request_data.get("data_type")
        data = request_data.get("data")

        # تحديد القنوات حسب نوع الإجراء
        channels = []
        if action_type == "accept_default":
            default_channel = redis_client.get('default_channel')
            if default_channel:
                channels = [default_channel]
        elif action_type == "accept_all":
            channels = list(redis_client.smembers('channels'))
        elif action_type == "accept_premium":
            # قنوات مميزة للطلبات المميزة
            premium_channels = list(redis_client.smembers('premium_channels'))
            channels = premium_channels if premium_channels else list(redis_client.smembers('channels'))

        if not channels:
            return {"success": False, "message": "لا توجد قنوات متاحة للنشر"}

        # تنسيق الرسالة
        if data_type == "usernames":
            formatted_data = ""
            for username in data.split('\n'):
                if username.strip():
                    nft_status = await NFTValidator.check_nft_status(username.strip())
                    nft_text = "NFT" if nft_status else "عادي"
                    formatted_data += f"@{username.strip()} ({nft_text}) | "
            message_text = MessageManager.format_auction_message("usernames", formatted_data)
        elif data_type == "username":
            nft_status = await NFTValidator.check_nft_status(data)
            nft_info = "(NFT) " if nft_status else "(عادي) "
            message_text = MessageManager.format_auction_message("username", data, nft_info)
        else:
            message_text = MessageManager.format_auction_message(data_type, data)

        # نشر في القنوات
        posted_links = []
        for channel in channels:
            try:
                msg = await bot.send_message(channel, message_text)
                posted_links.append(msg.link)
            except Exception as e:
                logger.error(f"فشل في النشر في القناة {channel}: {e}")

        if posted_links:
            DataManager.update_request_status(request_id, "accepted")

            # إشعار المستخدم
            links_text = "\n".join([f"• {link}" for link in posted_links])
            notification = (
                f"✅ **تم قبول طلبك وتم النشر!**\n\n"
                f"📢 **روابط المنشورات:**\n{links_text}\n\n"
                f"🎉 **شكراً لاستخدامك بوت مزاد ZWN**"
            )

            try:
                await bot.send_message(user_id, notification)
            except Exception as e:
                logger.error(f"فشل في إرسال الإشعار للمستخدم {user_id}: {e}")

            return {"success": True, "message": "تم قبول الطلب ونشره بنجاح", "links": posted_links}
        else:
            return {"success": False, "message": "فشل في النشر في جميع القنوات"}

# ===== فئة الإحصائيات =====
class StatsManager:
    @staticmethod
    def get_bot_statistics() -> Dict:
        """الحصول على إحصائيات البوت"""
        total_users = len(redis_client.smembers('users'))
        total_admins = len(redis_client.smembers('admins'))
        total_channels = len(redis_client.smembers('channels'))
        banned_users = len(redis_client.smembers('banned_users'))

        # إحصائيات الطلبات
        request_keys = redis_client.keys("request:*")
        total_requests = len(request_keys)

        pending_requests = 0
        accepted_requests = 0
        rejected_requests = 0

        for key in request_keys:
            status = redis_client.hget(key, "status")
            if status == "pending":
                pending_requests += 1
            elif status == "accepted":
                accepted_requests += 1
            elif status == "rejected":
                rejected_requests += 1

        return {
            "total_users": total_users,
            "total_admins": total_admins,
            "total_channels": total_channels,
            "banned_users": banned_users,
            "total_requests": total_requests,
            "pending_requests": pending_requests,
            "accepted_requests": accepted_requests,
            "rejected_requests": rejected_requests,
            "bot_status": redis_client.get('bot_status') or 'open'
        }

    @staticmethod
    def format_stats_message(stats: Dict) -> str:
        """تنسيق رسالة الإحصائيات"""
        return (
            f"📊 **إحصائيات بوت مزاد ZWN**\n\n"
            f"👥 **المستخدمون:** {stats['total_users']}\n"
            f"👨‍💼 **المشرفون:** {stats['total_admins']}\n"
            f"📢 **القنوات:** {stats['total_channels']}\n"
            f"🚫 **المحظورون:** {stats['banned_users']}\n\n"
            f"📋 **الطلبات:**\n"
            f"📝 المجموع: {stats['total_requests']}\n"
            f"⏳ المعلقة: {stats['pending_requests']}\n"
            f"✅ المقبولة: {stats['accepted_requests']}\n"
            f"❌ المرفوضة: {stats['rejected_requests']}\n\n"
            f"🔒 **حالة البوت:** {'🔓 مفتوح' if stats['bot_status'] == 'open' else '🔒 مغلق'}"
        )

# ===== معالجات الأحداث الرئيسية =====

@bot.on_message(filters.private & filters.command("start"))
async def start_command(_, message: Message):
    """معالج أمر البدء المحسن"""
    user_id = message.from_user.id

    # التحقق من الحظر
    if PermissionManager.is_banned(user_id):
        ban_info = redis_client.hgetall(f"ban:{user_id}")
        await message.reply(
            f"🚫 **تم حظرك من استخدام البوت**\n\n"
            f"📝 **السبب:** {ban_info.get('reason', 'غير محدد')}\n"
            f"📅 **تاريخ الحظر:** {ban_info.get('timestamp', 'غير محدد')}\n\n"
            f"📞 **للاستفسار:** @{BotConfig.OWNER_ID}"
        )
        return

    # التحقق من قفل البوت
    if redis_client.get('bot_status') == 'locked' and not PermissionManager.is_admin_or_owner(user_id):
        await message.reply(
            "🔒 **البوت مغلق حالياً للصيانة**\n\n"
            "⏰ سيتم فتحه قريباً، يرجى المحاولة لاحقاً\n"
            "📞 للاستفسار: @{BotConfig.OWNER_ID}"
        )
        return

    # تسجيل المستخدم الجديد
    if not redis_client.sismember('users', user_id):
        redis_client.sadd('users', user_id)
        redis_client.hset(f"user:{user_id}", mapping={
            "first_name": message.from_user.first_name or "",
            "last_name": message.from_user.last_name or "",
            "username": message.from_user.username or "",
            "join_date": datetime.now().isoformat()
        })

        # إشعار المالك بمستخدم جديد
        try:
            await bot.send_message(
                BotConfig.OWNER_ID,
                f"🎉 **مستخدم جديد انضم للبوت!**\n\n"
                f"👤 **الاسم:** {message.from_user.first_name}\n"
                f"🆔 **المعرف:** `{user_id}`\n"
                f"🔗 **اليوزر:** @{message.from_user.username or 'غير محدد'}\n"
                f"📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
        except Exception as e:
            logger.error(f"فشل في إرسال إشعار المستخدم الجديد: {e}")

    # عرض القائمة المناسبة
    is_admin = PermissionManager.is_admin_or_owner(user_id)
    welcome_msg = MessageManager.get_welcome_message(is_admin)
    keyboard = CustomKeyboards.main_menu_admin() if is_admin else CustomKeyboards.main_menu_user()

    await message.reply(welcome_msg, reply_markup=keyboard)

@bot.on_callback_query()
async def handle_callback_query(_, callback_query: CallbackQuery):
    """معالج الاستعلامات المحسن"""
    data = callback_query.data
    user_id = callback_query.from_user.id

    # التحقق من الحظر
    if PermissionManager.is_banned(user_id):
        await callback_query.answer("🚫 تم حظرك من استخدام البوت", show_alert=True)
        return

    try:
        if data == "back_home":
            is_admin = PermissionManager.is_admin_or_owner(user_id)
            welcome_msg = MessageManager.get_welcome_message(is_admin)
            keyboard = CustomKeyboards.main_menu_admin() if is_admin else CustomKeyboards.main_menu_user()
            await callback_query.message.edit(welcome_msg, reply_markup=keyboard)

        elif data == "back_to_user_menu":
            welcome_msg = MessageManager.get_welcome_message(False)
            keyboard = CustomKeyboards.main_menu_user()
            await callback_query.message.edit(welcome_msg, reply_markup=keyboard)

        elif data == "how_to_use":
            help_text = (
                "📖 **دليل استخدام بوت مزاد ZWN**\n\n"
                "🎯 **لإرسال معرف واحد:**\n"
                "• اضغط على 'إرسال معرف واحد'\n"
                "• أدخل المعرف بدون @\n"
                "• انتظر موافقة المشرفين\n\n"
                "👥 **لإرسال معرفات متعددة:**\n"
                "• اضغط على 'إرسال معرفات متعددة'\n"
                "• أدخل كل معرف في رسالة منفصلة\n"
                "• اكتب /end عند الانتهاء\n\n"
                "🎨 **لإرسال NFT:**\n"
                "• اضغط على الخيار المناسب\n"
                "• أدخل رابط NFT الصحيح\n"
                "• تأكد من صحة الرابط\n\n"
                "📊 **لمتابعة طلباتك:**\n"
                "• اضغط على 'حالة طلباتي'\n"
                "• ستظهر لك جميع طلباتك وحالتها"
            )
            await callback_query.message.edit(help_text, reply_markup=CustomKeyboards.back_button())

        elif data == "send_single_user":
            await handle_send_single_user(callback_query)

        elif data == "send_multiple_users":
            await handle_send_multiple_users(callback_query)

        elif data == "send_single_nft":
            await handle_send_single_nft(callback_query)

        elif data == "send_multiple_nfts":
            await handle_send_multiple_nfts(callback_query)

        elif data == "my_requests":
            await handle_my_requests(callback_query)

        elif data == "crypto_prices":
            await handle_crypto_prices(callback_query)

        # معالجات المشرفين
        elif data.startswith("accept_") or data.startswith("reject_") or data.startswith("ban_user_"):
            await handle_admin_actions(callback_query)

        elif data == "bot_stats" and PermissionManager.is_admin_or_owner(user_id):
            stats = StatsManager.get_bot_statistics()
            stats_msg = StatsManager.format_stats_message(stats)
            await callback_query.message.edit(stats_msg, reply_markup=CustomKeyboards.back_button())

        elif data == "toggle_bot_lock" and PermissionManager.is_admin_or_owner(user_id):
            await handle_toggle_bot_lock(callback_query)

        else:
            await callback_query.answer("⚠️ خيار غير متاح", show_alert=True)

    except Exception as e:
        logger.error(f"خطأ في معالجة الاستعلام {data}: {e}")
        await callback_query.answer("❌ حدث خطأ، يرجى المحاولة مرة أخرى", show_alert=True)

# ===== معالجات الطلبات =====

async def handle_send_single_user(callback_query: CallbackQuery):
    """معالج إرسال معرف واحد"""
    if redis_client.get('bot_status') == 'locked':
        await callback_query.answer("🔒 البوت مغلق حالياً", show_alert=True)
        return

    await callback_query.message.edit(
        "📝 **إرسال معرف تيليجرام واحد**\n\n"
        "🔸 أرسل المعرف الآن (بدون @)\n"
        "🔸 مثال: username123\n"
        "🔸 سيتم التحقق من حالة NFT تلقائياً\n\n"
        "⏰ **انتظر الرد...**",
        reply_markup=CustomKeyboards.back_button()
    )

    try:
        username_message = await callback_query.message.chat.ask(
            "📝 **أدخل المعرف الآن:**",
            filters=filters.text,
            timeout=300
        )

        username = username_message.text.replace('@', '').strip()

        if not username or len(username) < 3:
            await username_message.reply("❌ المعرف غير صحيح، يجب أن يكون 3 أحرف على الأقل")
            return

        await username_message.reply("⏳ **جاري إرسال طلبك للمراجعة...**")

        request_id = await RequestHandler.send_request_to_admins(
            callback_query.from_user, username, "username"
        )

        await username_message.reply(
            f"✅ **تم إرسال طلبك بنجاح!**\n\n"
            f"🆔 **رقم الطلب:** `{request_id[:8]}...`\n"
            f"📝 **المعرف:** @{username}\n"
            f"⏰ **سيتم مراجعته من قبل المشرفين**\n\n"
            f"💡 يمكنك متابعة حالة طلبك من 'حالة طلباتي'"
        )

    except asyncio.TimeoutError:
        await callback_query.message.edit(
            "⏰ **انتهت مهلة الانتظار**\n\nيرجى المحاولة مرة أخرى",
            reply_markup=CustomKeyboards.back_button()
        )
    except Exception as e:
        logger.error(f"خطأ في إرسال معرف واحد: {e}")
        await callback_query.message.edit(
            "❌ **حدث خطأ**\n\nيرجى المحاولة مرة أخرى",
            reply_markup=CustomKeyboards.back_button()
        )

async def handle_send_multiple_users(callback_query: CallbackQuery):
    """معالج إرسال معرفات متعددة"""
    if redis_client.get('bot_status') == 'locked':
        await callback_query.answer("🔒 البوت مغلق حالياً", show_alert=True)
        return

    await callback_query.message.edit(
        "👥 **إرسال معرفات متعددة**\n\n"
        "🔸 أرسل المعرفات واحداً تلو الآخر\n"
        "🔸 اكتب /end عند الانتهاء\n"
        "🔸 الحد الأقصى: 10 معرفات\n\n"
        "⏰ **ابدأ الآن...**",
        reply_markup=CustomKeyboards.back_button()
    )

    usernames = []
    max_usernames = 10

    try:
        while len(usernames) < max_usernames:
            user_input = await callback_query.message.chat.ask(
                f"📝 **أدخل المعرف رقم {len(usernames) + 1}** (أو /end للإنهاء):",
                filters=filters.text,
                timeout=300
            )

            if user_input.text.strip() == "/end":
                break

            username = user_input.text.replace('@', '').strip()

            if not username or len(username) < 3:
                await user_input.reply("❌ المعرف غير صحيح، أدخل معرف صحيح")
                continue

            if username in usernames:
                await user_input.reply("⚠️ هذا المعرف مضاف مسبقاً")
                continue

            usernames.append(username)
            await user_input.reply(f"✅ تم حفظ المعرف ({len(usernames)}/{max_usernames})")

        if not usernames:
            await callback_query.message.edit(
                "❌ **لم يتم إدخال أي معرفات**",
                reply_markup=CustomKeyboards.back_button()
            )
            return

        usernames_text = '\n'.join(usernames)

        await callback_query.message.chat.send_message("⏳ **جاري إرسال طلبك للمراجعة...**")

        request_id = await RequestHandler.send_request_to_admins(
            callback_query.from_user, usernames_text, "usernames"
        )

        await callback_query.message.chat.send_message(
            f"✅ **تم إرسال طلبك بنجاح!**\n\n"
            f"🆔 **رقم الطلب:** `{request_id[:8]}...`\n"
            f"📝 **عدد المعرفات:** {len(usernames)}\n"
            f"⏰ **سيتم مراجعته من قبل المشرفين**\n\n"
            f"💡 يمكنك متابعة حالة طلبك من 'حالة طلباتي'"
        )

    except asyncio.TimeoutError:
        await callback_query.message.edit(
            "⏰ **انتهت مهلة الانتظار**\n\nيرجى المحاولة مرة أخرى",
            reply_markup=CustomKeyboards.back_button()
        )
    except Exception as e:
        logger.error(f"خطأ في إرسال معرفات متعددة: {e}")
        await callback_query.message.edit(
            "❌ **حدث خطأ**\n\nيرجى المحاولة مرة أخرى",
            reply_markup=CustomKeyboards.back_button()
        )

async def handle_send_single_nft(callback_query: CallbackQuery):
    """معالج إرسال رابط NFT واحد"""
    if redis_client.get('bot_status') == 'locked':
        await callback_query.answer("🔒 البوت مغلق حالياً", show_alert=True)
        return

    await callback_query.message.edit(
        "🎨 **إرسال رابط NFT واحد**\n\n"
        "🔸 أرسل رابط NFT الآن\n"
        "🔸 يجب أن يبدأ بـ: t.me/nft/\n"
        "🔸 مثال: t.me/nft/example\n\n"
        "⏰ **انتظر الرد...**",
        reply_markup=CustomKeyboards.back_button()
    )

    try:
        nft_message = await callback_query.message.chat.ask(
            "🎨 **أدخل رابط NFT الآن:**",
            filters=filters.text,
            timeout=300
        )

        nft_link = nft_message.text.strip()

        if not await NFTValidator.validate_nft_link(nft_link):
            await nft_message.reply("❌ رابط NFT غير صحيح، يجب أن يبدأ بـ t.me/nft/")
            return

        await nft_message.reply("⏳ **جاري إرسال طلبك للمراجعة...**")

        request_id = await RequestHandler.send_request_to_admins(
            callback_query.from_user, nft_link, "NFT link"
        )

        await nft_message.reply(
            f"✅ **تم إرسال طلبك بنجاح!**\n\n"
            f"🆔 **رقم الطلب:** `{request_id[:8]}...`\n"
            f"🎨 **رابط NFT:** {nft_link}\n"
            f"⏰ **سيتم مراجعته من قبل المشرفين**\n\n"
            f"💡 يمكنك متابعة حالة طلبك من 'حالة طلباتي'"
        )

    except asyncio.TimeoutError:
        await callback_query.message.edit(
            "⏰ **انتهت مهلة الانتظار**\n\nيرجى المحاولة مرة أخرى",
            reply_markup=CustomKeyboards.back_button()
        )
    except Exception as e:
        logger.error(f"خطأ في إرسال NFT واحد: {e}")
        await callback_query.message.edit(
            "❌ **حدث خطأ**\n\nيرجى المحاولة مرة أخرى",
            reply_markup=CustomKeyboards.back_button()
        )

async def handle_send_multiple_nfts(callback_query: CallbackQuery):
    """معالج إرسال روابط NFT متعددة"""
    if redis_client.get('bot_status') == 'locked':
        await callback_query.answer("🔒 البوت مغلق حالياً", show_alert=True)
        return

    await callback_query.message.edit(
        "🖼️ **إرسال روابط NFT متعددة**\n\n"
        "🔸 أرسل روابط NFT واحداً تلو الآخر\n"
        "🔸 اكتب /end عند الانتهاء\n"
        "🔸 الحد الأقصى: 5 روابط\n\n"
        "⏰ **ابدأ الآن...**",
        reply_markup=CustomKeyboards.back_button()
    )

    nft_links = []
    max_nfts = 5

    try:
        while len(nft_links) < max_nfts:
            user_input = await callback_query.message.chat.ask(
                f"🎨 **أدخل رابط NFT رقم {len(nft_links) + 1}** (أو /end للإنهاء):",
                filters=filters.text,
                timeout=300
            )

            if user_input.text.strip() == "/end":
                break

            nft_link = user_input.text.strip()

            if not await NFTValidator.validate_nft_link(nft_link):
                await user_input.reply("❌ رابط NFT غير صحيح، يجب أن يبدأ بـ t.me/nft/")
                continue

            if nft_link in nft_links:
                await user_input.reply("⚠️ هذا الرابط مضاف مسبقاً")
                continue

            nft_links.append(nft_link)
            await user_input.reply(f"✅ تم حفظ الرابط ({len(nft_links)}/{max_nfts})")

        if not nft_links:
            await callback_query.message.edit(
                "❌ **لم يتم إدخال أي روابط**",
                reply_markup=CustomKeyboards.back_button()
            )
            return

        nft_links_text = '\n'.join(nft_links)

        await callback_query.message.chat.send_message("⏳ **جاري إرسال طلبك للمراجعة...**")

        request_id = await RequestHandler.send_request_to_admins(
            callback_query.from_user, nft_links_text, "NFT links"
        )

        await callback_query.message.chat.send_message(
            f"✅ **تم إرسال طلبك بنجاح!**\n\n"
            f"🆔 **رقم الطلب:** `{request_id[:8]}...`\n"
            f"🖼️ **عدد الروابط:** {len(nft_links)}\n"
            f"⏰ **سيتم مراجعته من قبل المشرفين**\n\n"
            f"💡 يمكنك متابعة حالة طلبك من 'حالة طلباتي'"
        )

    except asyncio.TimeoutError:
        await callback_query.message.edit(
            "⏰ **انتهت مهلة الانتظار**\n\nيرجى المحاولة مرة أخرى",
            reply_markup=CustomKeyboards.back_button()
        )
    except Exception as e:
        logger.error(f"خطأ في إرسال NFTs متعددة: {e}")
        await callback_query.message.edit(
            "❌ **حدث خطأ**\n\nيرجى المحاولة مرة أخرى",
            reply_markup=CustomKeyboards.back_button()
        )

async def handle_my_requests(callback_query: CallbackQuery):
    """معالج عرض طلبات المستخدم"""
    user_requests = DataManager.get_user_requests(callback_query.from_user.id)

    if not user_requests:
        await callback_query.message.edit(
            "📋 **طلباتي**\n\n"
            "❌ **لا توجد طلبات**\n\n"
            "💡 يمكنك إرسال طلب جديد من القائمة الرئيسية",
            reply_markup=CustomKeyboards.back_button()
        )
        return

    # عرض آخر 5 طلبات
    recent_requests = user_requests[:5]
    requests_text = "📋 **طلباتي الأخيرة**\n\n"

    for i, request in enumerate(recent_requests, 1):
        status_emoji = {
            "pending": "⏳",
            "accepted": "✅",
            "rejected": "❌"
        }.get(request.get("status", "pending"), "❓")

        status_text = {
            "pending": "قيد المراجعة",
            "accepted": "مقبول",
            "rejected": "مرفوض"
        }.get(request.get("status", "pending"), "غير محدد")

        data_type_ar = {
            "username": "معرف واحد",
            "usernames": "معرفات متعددة",
            "NFT link": "رابط NFT",
            "NFT links": "روابط NFT متعددة"
        }.get(request.get("data_type", ""), "غير محدد")

        timestamp = request.get("timestamp", "")
        if timestamp:
            try:
                dt = datetime.fromisoformat(timestamp)
                time_str = dt.strftime("%Y-%m-%d %H:%M")
            except:
                time_str = "غير محدد"
        else:
            time_str = "غير محدد"

        requests_text += (
            f"{status_emoji} **الطلب {i}**\n"
            f"📝 النوع: {data_type_ar}\n"
            f"📊 الحالة: {status_text}\n"
            f"📅 التاريخ: {time_str}\n"
            f"🆔 المعرف: `{request.get('request_id', '')[:8]}...`\n\n"
        )

    if len(user_requests) > 5:
        requests_text += f"📌 **وهناك {len(user_requests) - 5} طلبات أخرى**"

    await callback_query.message.edit(
        requests_text,
        reply_markup=CustomKeyboards.back_button()
    )

async def handle_crypto_prices(callback_query: CallbackQuery):
    """معالج عرض أسعار العملات المشفرة"""
    # هذه ميزة إضافية - يمكن ربطها بـ API حقيقي للأسعار
    prices_text = (
        "💰 **أسعار العملات المشفرة**\n\n"
        "🪙 **Bitcoin (BTC):** $43,250\n"
        "💎 **Ethereum (ETH):** $2,580\n"
        "🚀 **TON:** $2.45\n"
        "⭐ **USDT:** $1.00\n"
        "🔥 **BNB:** $315\n\n"
        "📊 **آخر تحديث:** منذ 5 دقائق\n"
        "⚠️ **تنبيه:** الأسعار تتغير باستمرار\n\n"
        "💡 **نصيحة:** تأكد من السعر الحالي قبل المزايدة"
    )

    await callback_query.message.edit(
        prices_text,
        reply_markup=CustomKeyboards.back_button()
    )

async def handle_admin_actions(callback_query: CallbackQuery):
    """معالج إجراءات المشرفين"""
    if not PermissionManager.is_admin_or_owner(callback_query.from_user.id):
        await callback_query.answer("❌ ليس لديك صلاحية لهذا الإجراء", show_alert=True)
        return

    data = callback_query.data
    action_type = data.split("/")[0]
    request_id = data.split("/")[1] if "/" in data else ""

    if action_type.startswith("accept_"):
        result = await RequestHandler.process_request_acceptance(
            request_id, action_type, callback_query.from_user.id
        )

        if result["success"]:
            await callback_query.message.edit(
                f"✅ **تم قبول الطلب بنجاح**\n\n"
                f"📢 **تم النشر في:** {len(result.get('links', []))} قناة\n"
                f"🆔 **معرف الطلب:** `{request_id[:8]}...`"
            )
        else:
            await callback_query.message.edit(f"❌ **خطأ:** {result['message']}")

    elif action_type == "reject":
        request_data = DataManager.get_request_data(request_id)
        if request_data:
            DataManager.update_request_status(request_id, "rejected")
            user_id = int(request_data.get("user_id"))

            try:
                await bot.send_message(
                    user_id,
                    f"❌ **تم رفض طلبك**\n\n"
                    f"🆔 **معرف الطلب:** `{request_id[:8]}...`\n"
                    f"📝 **السبب:** لم يتم استيفاء الشروط\n\n"
                    f"💡 يمكنك إرسال طلب جديد محسن"
                )
            except Exception as e:
                logger.error(f"فشل في إرسال إشعار الرفض: {e}")

            await callback_query.message.edit(
                f"❌ **تم رفض الطلب**\n\n"
                f"🆔 **معرف الطلب:** `{request_id[:8]}...`\n"
                f"📤 **تم إشعار المستخدم**"
            )
        else:
            await callback_query.answer("❌ طلب غير صحيح", show_alert=True)

    elif action_type == "ban_user":
        request_data = DataManager.get_request_data(request_id)
        if request_data:
            user_id = int(request_data.get("user_id"))
            PermissionManager.ban_user(user_id, "مخالفة قوانين المزاد")
            DataManager.update_request_status(request_id, "rejected")

            try:
                await bot.send_message(
                    user_id,
                    "🚫 **تم حظرك من استخدام البوت**\n\n"
                    "📝 **السبب:** مخالفة قوانين المزاد\n"
                    "📞 **للاستفسار:** تواصل مع الإدارة"
                )
            except Exception as e:
                logger.error(f"فشل في إرسال إشعار الحظر: {e}")

            await callback_query.message.edit(
                f"🚫 **تم حظر المستخدم**\n\n"
                f"👤 **معرف المستخدم:** `{user_id}`\n"
                f"📝 **السبب:** مخالفة قوانين المزاد"
            )
        else:
            await callback_query.answer("❌ طلب غير صحيح", show_alert=True)

async def handle_toggle_bot_lock(callback_query: CallbackQuery):
    """معالج قفل/فتح البوت"""
    current_status = redis_client.get('bot_status') or 'open'
    new_status = 'locked' if current_status == 'open' else 'open'

    redis_client.set('bot_status', new_status)

    status_text = "🔒 مغلق" if new_status == 'locked' else "🔓 مفتوح"
    action_text = "تم قفل" if new_status == 'locked' else "تم فتح"

    await callback_query.message.edit(
        f"🔄 **تم تغيير حالة البوت**\n\n"
        f"📊 **الحالة الحالية:** {status_text}\n"
        f"✅ **{action_text} البوت بنجاح**\n\n"
        f"💡 المستخدمون {'لن يتمكنوا' if new_status == 'locked' else 'يمكنهم'} من استخدام البوت",
        reply_markup=CustomKeyboards.back_button()
    )

# ===== أوامر المشرفين =====

@bot.on_message(filters.private & filters.command("broadcast"))
async def broadcast_command(_, message: Message):
    """أمر الإعلان للجميع"""
    if not PermissionManager.is_owner(message.from_user.id):
        await message.reply("❌ **هذا الأمر متاح للمالك فقط**")
        return

    try:
        broadcast_msg = await message.chat.ask(
            "📢 **أرسل رسالة الإعلان الآن:**\n\n"
            "💡 يمكنك إرسال نص أو صورة أو فيديو",
            filters=filters.text | filters.photo | filters.video | filters.document,
            timeout=300
        )

        user_ids = list(redis_client.smembers('users'))
        success_count = 0
        failed_count = 0

        progress_msg = await message.reply(f"📤 **جاري الإرسال...**\n\n📊 **التقدم:** 0/{len(user_ids)}")

        for i, user_id in enumerate(user_ids):
            try:
                if broadcast_msg.text:
                    await bot.send_message(user_id, broadcast_msg.text)
                else:
                    await bot.copy_message(user_id, broadcast_msg.chat.id, broadcast_msg.id)
                success_count += 1
            except Exception as e:
                failed_count += 1
                logger.error(f"فشل في إرسال الإعلان للمستخدم {user_id}: {e}")

            # تحديث التقدم كل 10 مستخدمين
            if (i + 1) % 10 == 0:
                await progress_msg.edit_text(
                    f"📤 **جاري الإرسال...**\n\n"
                    f"📊 **التقدم:** {i + 1}/{len(user_ids)}\n"
                    f"✅ **نجح:** {success_count}\n"
                    f"❌ **فشل:** {failed_count}"
                )

        await progress_msg.edit_text(
            f"✅ **تم إنهاء الإعلان**\n\n"
            f"📊 **النتائج النهائية:**\n"
            f"👥 **إجمالي المستخدمين:** {len(user_ids)}\n"
            f"✅ **تم الإرسال بنجاح:** {success_count}\n"
            f"❌ **فشل في الإرسال:** {failed_count}\n"
            f"📈 **معدل النجاح:** {(success_count/len(user_ids)*100):.1f}%"
        )

    except asyncio.TimeoutError:
        await message.reply("⏰ **انتهت مهلة الانتظار**")
    except Exception as e:
        logger.error(f"خطأ في الإعلان: {e}")
        await message.reply("❌ **حدث خطأ في الإعلان**")

@bot.on_message(filters.private & filters.command("add_admin"))
async def add_admin_command(_, message: Message):
    """أمر إضافة مشرف"""
    if not PermissionManager.is_owner(message.from_user.id):
        await message.reply("❌ **هذا الأمر متاح للمالك فقط**")
        return

    args = message.text.split()
    if len(args) < 2:
        await message.reply(
            "📝 **طريقة الاستخدام:**\n\n"
            "`/add_admin <معرف_المستخدم>`\n\n"
            "**مثال:** `/add_admin 123456789`"
        )
        return

    try:
        admin_id = int(args[1])
        redis_client.sadd('admins', admin_id)

        await message.reply(
            f"✅ **تم إضافة مشرف جديد**\n\n"
            f"👤 **معرف المستخدم:** `{admin_id}`\n"
            f"🎯 **الصلاحيات:** مراجعة وقبول/رفض الطلبات"
        )

        # إشعار المشرف الجديد
        try:
            await bot.send_message(
                admin_id,
                "🎉 **تهانينا! تم منحك صلاحيات الإشراف**\n\n"
                "🔹 يمكنك الآن مراجعة طلبات المستخدمين\n"
                "🔹 قبول أو رفض طلبات المزاد\n"
                "🔹 الوصول للوحة التحكم الإدارية\n\n"
                "💡 استخدم /start للوصول للوحة التحكم"
            )
        except Exception as e:
            logger.error(f"فشل في إشعار المشرف الجديد: {e}")

    except ValueError:
        await message.reply("❌ **معرف المستخدم يجب أن يكون رقماً**")
    except Exception as e:
        logger.error(f"خطأ في إضافة مشرف: {e}")
        await message.reply("❌ **حدث خطأ في إضافة المشرف**")

@bot.on_message(filters.private & filters.command("add_channel"))
async def add_channel_command(_, message: Message):
    """أمر إضافة قناة"""
    if not PermissionManager.is_admin_or_owner(message.from_user.id):
        await message.reply("❌ **ليس لديك صلاحية لهذا الأمر**")
        return

    try:
        channel_msg = await message.chat.ask(
            "📢 **أدخل معرف القناة:**\n\n"
            "🔸 بدون @ (مثال: channel_name)\n"
            "🔸 أو معرف رقمي (مثال: -1001234567890)",
            filters=filters.text,
            timeout=300
        )

        channel_id = channel_msg.text.replace('@', '').strip()

        if redis_client.sismember('channels', channel_id):
            await channel_msg.reply("⚠️ **هذه القناة مضافة مسبقاً**")
            return

        # اختبار إمكانية الإرسال للقناة
        try:
            test_msg = await bot.send_message(
                channel_id,
                "🧪 **رسالة اختبار** - تم إضافة القناة بنجاح!"
            )
            await test_msg.delete()

            redis_client.sadd('channels', channel_id)
            await channel_msg.reply(
                f"✅ **تم إضافة القناة بنجاح**\n\n"
                f"📢 **معرف القناة:** `{channel_id}`\n"
                f"🎯 **الحالة:** نشطة ومتاحة للنشر"
            )

        except Exception as e:
            await channel_msg.reply(
                f"❌ **فشل في إضافة القناة**\n\n"
                f"📝 **السبب:** {str(e)}\n"
                f"💡 **تأكد من:** إضافة البوت كمشرف في القناة"
            )

    except asyncio.TimeoutError:
        await message.reply("⏰ **انتهت مهلة الانتظار**")
    except Exception as e:
        logger.error(f"خطأ في إضافة قناة: {e}")
        await message.reply("❌ **حدث خطأ في إضافة القناة**")

@bot.on_message(filters.private & filters.command("set_default"))
async def set_default_channel_command(_, message: Message):
    """أمر تعيين القناة الافتراضية"""
    if not PermissionManager.is_admin_or_owner(message.from_user.id):
        await message.reply("❌ **ليس لديك صلاحية لهذا الأمر**")
        return

    channels = list(redis_client.smembers('channels'))
    if not channels:
        await message.reply("❌ **لا توجد قنوات مضافة**\n\nاستخدم `/add_channel` لإضافة قناة أولاً")
        return

    channels_text = "\n".join([f"• `{ch}`" for ch in channels])

    try:
        channel_msg = await message.chat.ask(
            f"📢 **اختر القناة الافتراضية:**\n\n"
            f"📋 **القنوات المتاحة:**\n{channels_text}\n\n"
            f"📝 **أدخل معرف القناة:**",
            filters=filters.text,
            timeout=300
        )

        channel_id = channel_msg.text.replace('@', '').strip()

        if channel_id not in channels:
            await channel_msg.reply("❌ **هذه القناة غير موجودة في القائمة**")
            return

        redis_client.set('default_channel', channel_id)
        await channel_msg.reply(
            f"✅ **تم تعيين القناة الافتراضية**\n\n"
            f"📢 **القناة:** `{channel_id}`\n"
            f"🎯 **ستستخدم هذه القناة للنشر الافتراضي**"
        )

    except asyncio.TimeoutError:
        await message.reply("⏰ **انتهت مهلة الانتظار**")
    except Exception as e:
        logger.error(f"خطأ في تعيين القناة الافتراضية: {e}")
        await message.reply("❌ **حدث خطأ في تعيين القناة**")

# ===== معالج رسائل المجموعة =====

@bot.on_message(filters.chat(BotConfig.GROUP_ID) & filters.group)
async def group_message_handler(_, message: Message):
    """معالج رسائل المجموعة المحسن"""
    if "Auction channel - @o_u_m" in message.text:
        auction_rules = (
            "🎯 **قوانين المزاد - يرجى الالتزام**\n\n"
            "🚫 **ممنوع منعاً باتاً:**\n"
            "• الكلام داخل المناقشة\n"
            "• دفع سعر وعدم الشراء\n"
            "• المزايدة الوهمية\n\n"
            "✅ **المطلوب:**\n"
            "• المزايدة بالتدريج مع العملة\n"
            "• الالتزام بالشراء عند الفوز\n"
            "• احترام المزايدين الآخرين\n\n"
            "💰 **أمثلة صحيحة للمزايدة:**\n"
            "• `5$` أو `10 TON` أو `50 AS`\n"
            "• `100 ج` أو `20 USDT`\n\n"
            "⚠️ **تحذير:** أي مخالفة = كتم فوري\n"
            "📢 **قناة المزاد:** @o_u_m"
        )

        await message.reply(
            auction_rules,
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("📢 قناة المزاد", url="t.me/o_u_m")],
                [InlineKeyboardButton("📞 التواصل مع الإدارة", user_id=BotConfig.OWNER_ID)]
            ])
        )

# ===== تشغيل البوت =====

async def main():
    """الدالة الرئيسية لتشغيل البوت"""
    try:
        await bot.start()
        bot_info = await bot.get_me()
        logger.info(f"🚀 تم تشغيل البوت بنجاح: @{bot_info.username}")

        # إشعار المالك بتشغيل البوت
        try:
            await bot.send_message(
                BotConfig.OWNER_ID,
                f"🎉 **تم تشغيل بوت مزاد ZWN المطور بنجاح!**\n\n"
                f"🤖 **اسم البوت:** @{bot_info.username}\n"
                f"🆔 **معرف البوت:** `{bot_info.id}`\n"
                f"⏰ **وقت التشغيل:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                f"✨ **الميزات الجديدة:**\n"
                f"• واجهة عربية كاملة\n"
                f"• لوحات مفاتيح مخصصة\n"
                f"• نظام طلبات محسن\n"
                f"• إحصائيات متقدمة\n"
                f"• إدارة محسنة للمشرفين\n\n"
                f"🔥 **البوت جاهز للعمل!**"
            )
        except Exception as e:
            logger.error(f"فشل في إرسال إشعار التشغيل: {e}")

        # تشغيل البوت
        await idle()

    except Exception as e:
        logger.error(f"خطأ في تشغيل البوت: {e}")
    finally:
        await bot.stop()
        redis_client.close()
        logger.info("🛑 تم إيقاف البوت")

if __name__ == "__main__":
    """نقطة دخول البرنامج"""
    print("🚀 بدء تشغيل بوت مزاد ZWN المطور...")
    print("📋 تحميل الإعدادات...")
    print("🔗 الاتصال بقاعدة البيانات...")
    print("⚡ تهيئة البوت...")

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ فادح: {e}")
        logger.error(f"خطأ فادح في البرنامج: {e}")
    finally:
        print("👋 شكراً لاستخدام بوت مزاد ZWN المطور!")
